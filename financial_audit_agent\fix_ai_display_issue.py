#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复AI显示问题的脚本
清理可能的缓存问题，确保显示正确的AI思考内容
"""

import json
import time
from pathlib import Path

def clean_ai_thinking_content():
    """清理并重新设置AI思考内容"""
    print("🧹 清理AI思考内容...")
    
    # 读取当前状态
    state_file = Path("backend/audit_state.json")
    with open(state_file, 'r', encoding='utf-8') as f:
        state_data = json.load(f)
    
    # 检查当前内容
    current_thinking = state_data.get('ai_thinking', '')
    print(f"📊 当前内容长度: {len(current_thinking)} 字符")
    
    # 如果内容包含对话历史，则清理
    if any(pattern in current_thinking for pattern in [
        "好的！我将立即开始执行",
        "🚀 开始执行阶段调整", 
        "步骤1：备份原文件",
        "Created file",
        "Edited file"
    ]):
        print("⚠️ 检测到对话历史内容，正在清理...")
        
        # 生成正确的AI思考内容
        clean_content = generate_proper_ai_thinking(state_data)
        state_data['ai_thinking'] = clean_content
        state_data['last_updated'] = time.strftime("%Y-%m-%dT%H:%M:%SZ")
        
        # 写回文件
        with open(state_file, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, ensure_ascii=False, indent=2)
        
        print("✅ AI思考内容已清理并重新生成")
        return True
    else:
        print("✅ AI思考内容格式正常")
        return False

def generate_proper_ai_thinking(state_data):
    """根据当前状态生成正确的AI思考内容"""
    current_phase = state_data.get('current_phase', 'attachment-check')
    progress = state_data.get('progress_percent', 0)
    
    if current_phase == 'attachment-check' or progress <= 25:
        return generate_attachment_thinking()
    elif current_phase == 'field-consistency' or progress <= 63:
        return generate_consistency_thinking()
    elif current_phase == 'amount-standard' or progress <= 79:
        return generate_amount_thinking()
    elif current_phase == 'compliance-check' or progress <= 100:
        return generate_compliance_thinking()
    else:
        return generate_default_thinking()

def generate_attachment_thinking():
    """生成附件完整性检查的思考内容"""
    return """## 🔍 附件完整性检查 (阶段 1/4)

### 📋 当前审核阶段分析

我正在执行第一阶段的附件完整性检查，这是整个审核流程的基础环节。需要验证以下5条关键规则：

**规则1：检查是否上传发票**
- 检查路径：[来源: 附件概览 -> 附件类型]
- 验证标准：列表中必须包含"发票"
- 业务意义：发票是报销的法定凭证

**规则2：检查是否上传事前审批表**
- 检查路径：[来源: 附件概览 -> 附件类型]  
- 验证标准：列表中必须包含"业务招待事前审批表"
- 业务意义：确保招待活动经过事前审批

**规则3：检查是否上传用餐小票**
- 检查路径：[来源: 附件概览 -> 附件类型]
- 验证标准：列表中必须包含"餐饮小票"
- 业务意义：提供消费明细的详细记录

**规则4：检查是否上传支付记录**
- 检查路径：[来源: 附件概览 -> 附件类型]
- 验证标准：列表中必须包含"支付记录"
- 业务意义：证明实际支付行为

**规则5：检查特殊物品签收表**
- 检查路径：[来源: 主报销单信息 -> 事由]
- 验证标准：如果事由中提及非餐饮物品，需要签收表
- 业务意义：确保特殊物品的发放有据可查

### 🔄 正在分析单据数据...

正在逐一验证附件完整性，确保所有必需的支撑材料都已提供。

### ✅ 附件完整性检查进行中

基础附件验证是审核流程的第一道防线，确保后续审核有充分的数据支撑。"""

def generate_consistency_thinking():
    """生成字段一致性检查的思考内容"""
    return """## 🔍 字段内容与一致性检查 (阶段 2/4)

### 📋 当前审核阶段分析

我正在执行第二阶段的字段内容与一致性检查，这是最复杂的审核环节，包含19条详细规则。

### 🔍 主要检查维度

**1. 招待对象一致性验证**
- 主报销单与审批表中的招待对象必须完全一致
- 确保没有信息不符或篡改

**2. 招待类型与发起主体匹配**
- 商务招待 → 业务部门发起
- 公务招待 → 行政部门发起

**3. 时间一致性检查**
- 招待日期、用餐日期、支付日期的逻辑关系
- 事前审批时间的合理性

**4. 人数信息核对**
- 招待人数、陪餐人数、总人数的数学关系
- 各文档中人数信息的一致性

**5. 金额数据验证**
- 小票金额、支付金额、报销金额的匹配
- 确保没有金额篡改或错误

### 🔄 正在执行深度一致性分析...

这个阶段需要交叉验证多个数据源，确保所有信息的逻辑一致性和真实性。

### ⚡ 字段一致性检查进行中

正在逐条验证19个一致性规则，这是确保审核质量的关键环节。"""

def generate_amount_thinking():
    """生成金额与标准检查的思考内容"""
    return """## 🔍 金额与标准检查 (阶段 3/4)

### 📋 当前审核阶段分析

我正在执行第三阶段的金额与标准检查，重点关注消费金额的合规性。

### 💰 主要检查项目

**1. 预算超支检查**
- 实际消费 vs 预计招待金额
- 确保在预算范围内

**2. 人均消费标准验证**
- 计算实际人均消费
- 对比公司餐饮标准

**3. 酒水使用合规性**
- 酒水金额的合理性
- 酒水使用情况的真实性

**4. 消费明细审查**
- 检查是否存在天价菜品
- 验证菜品数量的合理性

### 🔄 正在进行金额计算和标准对比...

通过精确的数学计算和标准对比，确保所有消费都在合理范围内。

### 💡 金额与标准检查进行中

正在验证6条金额相关规则，确保财务合规性。"""

def generate_compliance_thinking():
    """生成合规性检查的思考内容"""
    return """## 🔍 八项规定精神 (阶段 4/4)

### 📋 当前审核阶段分析

我正在执行最后阶段的八项规定合规性检查，确保严格遵守国家法规。

### ⚖️ 合规性检查要点

**1. 消费场所合规性**
- 禁止在会所、俱乐部、KTV等场所消费
- 确保消费场所的正当性

**2. 消费内容合规性**
- 禁止鱼翅、燕窝等奢侈消费
- 禁止高档烟酒消费

**3. 公务招待特殊要求**
- 公务招待必须有来函
- 禁止住宿费用报销

**4. 招待对象身份核查**
- 识别是否涉及公职人员
- 确保招待活动的正当性

### 🔄 正在进行合规性深度审查...

严格按照八项规定要求，逐项检查是否存在违规行为。

### 🛡️ 合规性检查进行中

正在验证8条合规性规则，确保完全符合国家法规要求。"""

def generate_default_thinking():
    """生成默认的思考内容"""
    return """## 🤖 AI审核引擎正在分析

### 📊 当前状态

正在进行深度审核分析，请稍候...

### 🔄 分析进行中

AI引擎正在处理复杂的审核逻辑，确保审核结果的准确性和完整性。

### ⚡ 请耐心等待

审核过程需要时间来确保质量，感谢您的耐心等待。"""

def clear_frontend_cache():
    """清理前端可能的缓存"""
    print("🧹 清理前端缓存...")
    
    # 创建一个简单的缓存清理指令文件
    cache_clear_file = Path("frontend/clear_cache.js")
    cache_clear_content = """
// 清理前端缓存的脚本
console.log('🧹 清理AI显示缓存...');

// 清理localStorage
if (typeof localStorage !== 'undefined') {
    localStorage.removeItem('ai_thinking_cache');
    localStorage.removeItem('last_thinking_content');
    console.log('✅ localStorage已清理');
}

// 清理sessionStorage  
if (typeof sessionStorage !== 'undefined') {
    sessionStorage.removeItem('ai_thinking_cache');
    sessionStorage.removeItem('last_thinking_content');
    console.log('✅ sessionStorage已清理');
}

// 强制刷新AI显示区域
const thinkingContent = document.getElementById('thinking-content');
if (thinkingContent) {
    thinkingContent.innerHTML = '<div class="thinking-step"><div class="step-indicator active"></div><div class="step-text">🔄 正在重新加载AI分析内容...</div></div>';
    console.log('✅ AI显示区域已重置');
}

console.log('🎉 缓存清理完成，请刷新页面');
"""
    
    with open(cache_clear_file, 'w', encoding='utf-8') as f:
        f.write(cache_clear_content)
    
    print("✅ 前端缓存清理脚本已创建")

def main():
    print("="*60)
    print("🔧 AI显示问题修复工具")
    print("="*60)
    
    # 清理AI思考内容
    content_cleaned = clean_ai_thinking_content()
    
    # 清理前端缓存
    clear_frontend_cache()
    
    print("\n🎉 修复完成！")
    print("\n📝 下一步操作:")
    print("  1. 重启后端服务: python start_backend_v2.py")
    print("  2. 强制刷新前端页面 (Ctrl+F5)")
    print("  3. 清除浏览器缓存 (Ctrl+Shift+Delete)")
    print("  4. 检查AI分析引擎显示是否正常")
    
    if content_cleaned:
        print("\n✅ AI思考内容已修复")
    else:
        print("\n💡 AI思考内容本身正常，问题可能在前端显示")
    
    print("\n🔍 如果问题仍然存在:")
    print("  - 打开浏览器开发者工具 (F12)")
    print("  - 查看Console标签页的错误信息")
    print("  - 检查Network标签页的API请求")
    print("  - 尝试在隐私模式下打开页面")

if __name__ == "__main__":
    main()
