#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新版核心编排器 - 分步式智能审核引擎
指挥整个审核流程,串联所有模块,管理审核状态,生成最终报告
"""

import json
import time
import os
import threading
from typing import Dict, List, Any, Optional, Tuple
try:
    # 尝试相对导入（作为包运行时）
    from .rule_parser import RuleParser
    from .data_consolidator import DataConsolidator
    from .prompt_manager import PromptManager
except ImportError:
    # 回退到绝对导入（直接运行时）
    from rule_parser import RuleParser
    from data_consolidator import DataConsolidator
    from prompt_manager import PromptManager


class ReportFileManager:
    """
    报告文件管理器：负责AI思维链数据的直接写入和读取
    实现增量更新审核报告文件，解决并发写入问题
    """

    def __init__(self, doc_num: str):
        """
        初始化报告文件管理器

        Args:
            doc_num: 文档编号，用于构建文件路径
        """
        self.doc_num = doc_num
        self.file_lock = threading.Lock()

        # 构建报告文件路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.report_file_path = os.path.join(
            project_root, 'audit_reports', f'audit_report_{doc_num}.json'
        )

        # 确保目录存在
        os.makedirs(os.path.dirname(self.report_file_path), exist_ok=True)

        # 初始化报告文件结构
        self._initialize_report_file()

    def _initialize_report_file(self):
        """初始化报告文件，创建基础结构"""
        if not os.path.exists(self.report_file_path):
            # 创建初始报告结构
            initial_report = {
                "summary": {
                    "total_rules_checked": 0,
                    "passed_count": 0,
                    "failed_count": 0,
                    "warning_count": 0
                },
                "details": [],
                "review_comments": "",
                "ai_thinking_chain": {
                    "combined_thinking": "审核分析进行中...",
                    "phases_history": {},
                    "extraction_metadata": {
                        "extracted_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "audit_id": self.doc_num,
                        "audit_status": "running",
                        "integration_version": "2.0"
                    }
                },
                "audit_metadata": {
                    "version": "2.0",
                    "audit_type": "stepwise_intelligent_audit",
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "document_number": self.doc_num,
                    "ai_thinking_included": True
                }
            }

            with open(self.report_file_path, 'w', encoding='utf-8') as f:
                json.dump(initial_report, f, ensure_ascii=False, indent=2)

    def update_ai_thinking(self, phase_key: str, ai_thinking: str, phase_name: str = None,
                          status: str = "running", message: str = "", detail: str = ""):
        """
        更新AI思维链数据到报告文件

        Args:
            phase_key: 阶段键值 (如 phase1, phase2)
            ai_thinking: AI思维链内容
            phase_name: 阶段名称
            status: 阶段状态
            message: 状态消息
            detail: 详细信息
        """
        with self.file_lock:
            try:
                # 读取现有报告
                with open(self.report_file_path, 'r', encoding='utf-8') as f:
                    report_data = json.load(f)

                current_time = time.strftime("%Y-%m-%dT%H:%M:%SZ")

                # 更新阶段历史
                if "ai_thinking_chain" not in report_data:
                    report_data["ai_thinking_chain"] = {
                        "combined_thinking": "",
                        "phases_history": {},
                        "extraction_metadata": {}
                    }

                # 添加或更新阶段数据
                report_data["ai_thinking_chain"]["phases_history"][phase_key] = {
                    "phase_name": phase_name or phase_key,
                    "ai_thinking": ai_thinking,
                    "status": status,
                    "timestamp": current_time,
                    "message": message,
                    "detail": detail
                }

                # 重新构建组合思维链
                combined_thinking = self._build_combined_thinking_from_phases(
                    report_data["ai_thinking_chain"]["phases_history"]
                )
                report_data["ai_thinking_chain"]["combined_thinking"] = combined_thinking

                # 更新元数据
                report_data["ai_thinking_chain"]["extraction_metadata"].update({
                    "last_updated": current_time,
                    "audit_status": status
                })

                # 写回文件
                with open(self.report_file_path, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, ensure_ascii=False, indent=2)

                print(f"[报告] AI思维链已更新到报告文件: {phase_key}")

            except Exception as e:
                print(f"[错误] 更新AI思维链失败: {e}")

    def _build_combined_thinking_from_phases(self, phases_history: Dict) -> str:
        """从阶段历史构建组合思维链"""
        combined_parts = []

        # 定义阶段顺序
        phase_order = [
            ("phase1", "🔍 附件完整性检查 (阶段 1/4)"),
            ("phase2", "🔍 字段内容与一致性检查 (阶段 2/4)"),
            ("phase3", "🔍 金额与标准检查 (阶段 3/4)"),
            ("phase4", "🔍 八项规定精神 (阶段 4/4)")
        ]

        for phase_key, phase_title in phase_order:
            if phase_key == "phase2":
                # 特殊处理第二部分：合并分组数据
                phase2_content = self._build_phase2_combined_content(phases_history)
                if phase2_content:
                    combined_parts.append(f"## {phase_title}")
                    combined_parts.append("")
                    combined_parts.append(phase2_content)
                    combined_parts.append("")
                    combined_parts.append("---")
                    combined_parts.append("")
            elif phase_key in phases_history:
                phase_data = phases_history[phase_key]
                combined_parts.append(f"## {phase_title}")
                combined_parts.append("")
                combined_parts.append(phase_data.get("ai_thinking", ""))
                combined_parts.append("")
                combined_parts.append("---")
                combined_parts.append("")

        return "\n".join(combined_parts)

    def _build_phase2_combined_content(self, phases_history: Dict) -> str:
        """构建第二部分的合并内容"""
        phase2_parts = []

        # 查找第二部分的分组数据
        group1_key = "第二部分：字段内容与一致性检查_(第1组)"
        group2_key = "第二部分：字段内容与一致性检查_(第2组)"

        # 添加第1组内容
        if group1_key in phases_history:
            group1_data = phases_history[group1_key]
            phase2_parts.append("### 第1组 (规则6-14)")
            phase2_parts.append("")
            phase2_parts.append(group1_data.get("ai_thinking", ""))
            phase2_parts.append("")

        # 添加第2组内容
        if group2_key in phases_history:
            group2_data = phases_history[group2_key]
            if phase2_parts:  # 如果已有第1组内容，添加分隔符
                phase2_parts.append("---")
                phase2_parts.append("")
            phase2_parts.append("### 第2组 (规则15-24)")
            phase2_parts.append("")
            phase2_parts.append(group2_data.get("ai_thinking", ""))
            phase2_parts.append("")

        # 如果没有找到分组数据，尝试查找标准的phase2
        if not phase2_parts and "phase2" in phases_history:
            phase2_data = phases_history["phase2"]
            phase2_parts.append(phase2_data.get("ai_thinking", ""))

        return "\n".join(phase2_parts)

    def update_audit_results(self, phase_key: str, phase_results: List[Dict], phase_name: str = None):
        """
        增量写入审核结果到报告文件

        Args:
            phase_key: 阶段键值 (如 phase1, phase2)
            phase_results: 该阶段的审核结果列表
            phase_name: 阶段名称
        """
        with self.file_lock:
            try:
                # 读取现有报告
                with open(self.report_file_path, 'r', encoding='utf-8') as f:
                    report_data = json.load(f)

                current_time = time.strftime("%Y-%m-%d %H:%M:%S")

                # 确保details是列表
                if "details" not in report_data:
                    report_data["details"] = []

                # 为每个结果添加阶段信息并追加到details
                for result in phase_results:
                    # 创建增强的结果对象
                    enhanced_result = {
                        "rule_id": result.get("rule_id", "未知规则"),
                        "status": self._normalize_status(result.get("status", "未知")),
                        "reason": result.get("reason", "无详细信息"),
                        "phase_key": phase_key,
                        "phase_name": phase_name or phase_key,
                        "timestamp": current_time
                    }

                    # 追加到details数组
                    report_data["details"].append(enhanced_result)

                # 重新计算summary统计
                self._update_summary_statistics(report_data)

                # 更新元数据
                report_data["audit_metadata"]["timestamp"] = current_time

                # 写回文件
                with open(self.report_file_path, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, ensure_ascii=False, indent=2)

                print(f"[报告] 阶段 {phase_key} 的审核结果已增量写入: {len(phase_results)} 条规则")

            except Exception as e:
                print(f"[错误] 增量写入审核结果失败: {e}")

    def _normalize_status(self, status: str) -> str:
        """标准化状态值"""
        status_lower = status.lower().strip()
        if status_lower in ["通过", "pass", "passed", "success", "成功"]:
            return "PASS"
        elif status_lower in ["警告", "warning", "warn", "注意"]:
            return "WARNING"
        elif status_lower in ["不通过", "失败", "fail", "failed", "error", "错误", "执行失败"]:
            return "FAIL"
        else:
            return "UNKNOWN"

    def _update_summary_statistics(self, report_data: Dict):
        """重新计算并更新summary统计信息"""
        details = report_data.get("details", [])

        # 统计各种状态的数量
        total_count = len(details)
        pass_count = sum(1 for d in details if d.get("status") == "PASS")
        warning_count = sum(1 for d in details if d.get("status") == "WARNING")
        fail_count = sum(1 for d in details if d.get("status") == "FAIL")

        # 更新summary
        report_data["summary"] = {
            "total_rules_checked": total_count,
            "passed_count": pass_count,
            "failed_count": fail_count,
            "warning_count": warning_count
        }

    def update_final_report(self, summary: Dict, details: List, review_comments: str):
        """
        更新最终报告数据（除AI思维链外的其他部分）
        注意：这个方法现在主要用于兼容性，实际的审核结果应该通过update_audit_results增量写入

        Args:
            summary: 审核摘要（可能被忽略，如果已有增量数据）
            details: 审核详情（可能被忽略，如果已有增量数据）
            review_comments: 审批意见
        """
        with self.file_lock:
            try:
                # 读取现有报告
                with open(self.report_file_path, 'r', encoding='utf-8') as f:
                    report_data = json.load(f)

                current_time = time.strftime("%Y-%m-%d %H:%M:%S")

                # 只更新review_comments，summary和details应该已经通过增量写入更新了
                report_data["review_comments"] = review_comments

                # 如果details为空（说明增量写入失败），则使用传统方式
                if not report_data.get("details"):
                    print("[警告] details为空，使用传统方式写入")
                    report_data["summary"] = summary
                    report_data["details"] = details
                else:
                    print(f"[信息] 使用增量写入的数据，当前details包含 {len(report_data['details'])} 条规则")

                # 更新元数据
                report_data["audit_metadata"]["timestamp"] = current_time

                # 标记审核完成
                if "ai_thinking_chain" in report_data:
                    report_data["ai_thinking_chain"]["extraction_metadata"]["audit_status"] = "completed"
                    report_data["ai_thinking_chain"]["extraction_metadata"]["completion_time"] = time.strftime("%Y-%m-%dT%H:%M:%SZ")

                # 写回文件
                with open(self.report_file_path, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, ensure_ascii=False, indent=2)

                print(f"[报告] 最终报告已更新: {self.report_file_path}")

            except Exception as e:
                print(f"[错误] 更新最终报告失败: {e}")

    def get_report_data(self) -> Dict[str, Any]:
        """
        获取当前报告数据

        Returns:
            Dict: 报告数据
        """
        try:
            with open(self.report_file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"[错误] 读取报告数据失败: {e}")
            return {}


class OrchestratorV2:
    """新版编排器:实现分步式智能审核流程"""

    def __init__(self, config: Dict[str, Any], llm_caller, doc_num: str = None):
        """
        初始化编排器

        Args:
            config: 配置字典
            llm_caller: LLM调用器实例
            doc_num: 文档编号,用于动态文件命名
        """
        self.config = config
        self.llm_caller = llm_caller
        self.prompt_manager = PromptManager()
        self.audit_results = []
        self.step_summaries = []
        self.doc_num = doc_num

        # 存储所有阶段的AI思维链
        self.all_thinking_chains = []

        # 初始化报告文件管理器（新架构的核心）
        if doc_num:
            self.report_manager = ReportFileManager(doc_num)
            print(f"[架构] 已启用新的报告文件管理器: audit_report_{doc_num}.json")
        else:
            self.report_manager = None
            print("[架构] 未提供文档编号，使用传统状态文件模式")

        # 唯一权威状态文件路径（保持兼容性）
        self.state_file_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'backend', 'audit_state.json'
        )

        # 项目根目录
        self.project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

    def _update_progress(self, step_id: str, status: str, message: str = "", detail: str = "",
                        audit_details: Optional[Dict] = None, final_stats: Optional[Dict] = None,
                        ai_thinking: Optional[str] = None):
        """
        更新审核进度状态文件（新版:单一权威状态文件）

        Args:
            step_id (str): 步骤ID,与前端对应.
            status (str): 状态 'running', 'completed', 'failed', 'audit-complete'.
            message (str): 显示给用户的消息.
            detail (str): 详细信息,如规则执行进度.
            audit_details (Dict, optional): 审核详细信息,用于显示当前阶段等.
            final_stats (Dict, optional): 最终统计信息.
            ai_thinking (str, optional): AI思考过程.
        """
        # 读取现有状态以保持连续性
        existing_state = {}
        try:
            if os.path.exists(self.state_file_path):
                with open(self.state_file_path, 'r', encoding='utf-8') as f:
                    existing_state = json.load(f)
        except Exception as e:
            print(f"[警告] 读取现有状态文件失败: {e}")

        # 构建标准化状态对象
        current_time = time.strftime("%Y-%m-%dT%H:%M:%SZ")

        # 映射旧状态到新状态格式
        audit_status = self._map_status_to_audit_status(status)
        current_phase = self._map_step_to_phase(step_id)
        progress_percent = self._calculate_progress_percent(step_id, status)

        # 构建改进的状态结构，保留所有阶段的信息
        phases_history = existing_state.get("phases_history", {})

        # 如果有新的AI思维链，添加到对应阶段
        if ai_thinking and current_phase != "ready":
            phases_history[current_phase] = {
                "phase_name": current_phase,
                "ai_thinking": ai_thinking,
                "status": audit_status,
                "timestamp": current_time,
                "message": message,
                "detail": detail
            }

        # 构建当前显示的AI思维链（包含所有阶段）
        combined_ai_thinking = self._build_combined_thinking(phases_history, ai_thinking, current_phase)

        state = {
            "audit_id": self.doc_num,
            "audit_status": audit_status,
            "current_phase": current_phase,
            "progress_percent": progress_percent,
            "start_time": existing_state.get("start_time") or (current_time if audit_status != "ready" else None),
            "completion_time": current_time if audit_status == "completed" else None,
            "summary": final_stats or existing_state.get("summary", {
                "total_rules": 0,
                "completed_rules": 0,
                "passed_rules": 0,
                "failed_rules": 0,
                "warning_rules": 0
            }),
            "ai_thinking": combined_ai_thinking,
            "phases_history": phases_history,  # 新增：保存所有阶段的历史
            "last_updated": current_time,
            "message": message,
            "detail": detail or ""
        }

        # 添加审核详情（如果有）
        if audit_details:
            state["audit_details"] = audit_details

        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.state_file_path), exist_ok=True)

            # 写入唯一权威状态文件（保持兼容性）
            with open(self.state_file_path, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)

            print(f"[状态] 更新权威状态文件: {self.state_file_path}")

        except Exception as e:
            print(f"[错误] 状态文件更新失败: {e}")

        # 新架构：同时更新报告文件中的AI思维链数据
        if self.report_manager and ai_thinking and current_phase != "ready":
            try:
                # 映射阶段到报告文件的键值
                phase_mapping = {
                    'attachment-check': 'phase1',
                    'field-consistency': 'phase2',
                    'amount-standard': 'phase3',
                    'compliance-check': 'phase4',
                    'finished': 'finished'
                }

                phase_key = phase_mapping.get(current_phase, current_phase)
                phase_name_mapping = {
                    'phase1': '附件完整性检查',
                    'phase2': '字段内容与一致性检查',
                    'phase3': '金额与标准检查',
                    'phase4': '八项规定精神',
                    'finished': '审核完成'
                }

                phase_name = phase_name_mapping.get(phase_key, current_phase)

                # 直接更新报告文件中的AI思维链
                self.report_manager.update_ai_thinking(
                    phase_key=phase_key,
                    ai_thinking=ai_thinking,
                    phase_name=phase_name,
                    status=audit_status,
                    message=message,
                    detail=detail
                )

                print(f"[新架构] AI思维链已直接写入报告文件: {phase_key}")

            except Exception as e:
                print(f"[错误] 报告文件AI思维链更新失败: {e}")

    def _map_status_to_audit_status(self, status: str) -> str:
        """映射旧状态到新的审核状态"""
        mapping = {
            'ready': 'ready',
            'running': 'running',
            'completed': 'running',
            'audit-complete': 'completed',
            'failed': 'failed'
        }
        return mapping.get(status, 'ready')

    def _map_step_to_phase(self, step_id: str) -> str:
        """映射步骤ID到审核阶段"""
        mapping = {
            'ready': 'ready',
            'rule-parsing': 'initializing',
            'data-consolidation': 'initializing',
            'attachment-check': 'attachment-check',
            'field-consistency': 'field-consistency',
            'amount-standard': 'amount-standard',
            'compliance-check': 'compliance-check',
            'report-generation': 'finished'
        }
        return mapping.get(step_id, 'ready')

    def _calculate_progress_percent(self, step_id: str, status: str) -> int:
        """计算进度百分比"""
        step_progress = {
            'ready': 0,
            'rule-parsing': 10,
            'data-consolidation': 20,
            'attachment-check': 40,
            'field-consistency': 60,
            'amount-standard': 80,
            'compliance-check': 90,
            'report-generation': 100
        }

        base_progress = step_progress.get(step_id, 0)
        if status == 'audit-complete':
            return 100
        elif status == 'completed' and step_id != 'report-generation':
            return min(base_progress + 10, 100)
        else:
            return base_progress




        
    def run_audit(self, form_path: str, attachment_path: str, rules_path: str) -> Dict[str, Any]:
        """
        执行完整的分步式审核流程
        
        Args:
            form_path: 表单JSON文件路径
            attachment_path: 附件JSON文件路径  
            rules_path: 规则文件路径
            
        Returns:
            Dict[str, Any]: 最终审核报告
        """
        print("[启动] 启动新版分步式智能审核系统...")
        start_time = time.time()

        try:
            # 1. 解析规则文件
            self._update_progress('rule-parsing', 'running', '正在解析规则文件...')
            print(" [1/7] 正在解析规则文件...")
            parser = RuleParser(rules_path)
            rule_groups = parser.get_rule_groups()

            if not rule_groups:
                error_msg = "规则文件解析失败或为空"
                self._update_progress('rule-parsing', 'failed', error_msg)
                return self._create_error_report(error_msg)

            print(f"     [成功] 成功解析 {len(rule_groups)} 个审核阶段")
            self._update_progress('rule-parsing', 'completed', '规则解析完成',
                                f'成功解析 {len(rule_groups)} 个审核阶段')
            
            # 2. 整合单据数据
            self._update_progress('data-consolidation', 'running', '正在整合单据数据...')
            print(" [2/7] 正在整合单据数据...")
            consolidator = DataConsolidator(form_path, attachment_path)
            data_text = consolidator.get_consolidated_text()

            # 提取单据编号用于动态文件名
            # 优先使用命令行传入的文档编号,如果没有则从数据中提取
            if self.doc_num:
                document_number = self.doc_num
                print(f"     [成功] 使用命令行指定的单据编号: {document_number}")
            else:
                document_number = consolidator.get_document_number()
                print(f"     [成功] 从数据中提取到单据编号: {document_number}")

            # 保存当前文档编号供报告生成使用
            self.current_document_number = document_number

            if not data_text.strip():
                error_msg = "单据数据为空或无法读取"
                self._update_progress('data-consolidation', 'failed', error_msg)
                return self._create_error_report(error_msg)

            print("     [成功] 数据整合完成")
            self._update_progress('data-consolidation', 'completed', '数据整合完成',
                                f'共整合 {len(data_text)} 字符的数据')

            # 3-6. 分步执行审核
            print(" [3-6/7] 开始分步式智能审核...")
            all_results = []
            audit_context_summary = ""

            # 定义步骤映射
            step_mapping = {
                "第一部分:附件完整性检查": "attachment-check",
                "第二部分:字段内容与一致性检查": "field-consistency",
                "第三部分:金额与标准检查": "amount-standard",
                "第四部分:八项规定精神": "compliance-check"
            }

            total_groups = len(rule_groups)
            for i, (group_name, rules_text) in enumerate(rule_groups.items(), 1):
                print(f"  [清单] 正在审核阶段 {i}/{total_groups}: {group_name}")

                # 获取对应的步骤ID
                step_id = step_mapping.get(group_name, 'audit-execution')

                # 计算规则数量
                rule_count = self._count_rules_in_text(rules_text)
                detail_msg = f'包含 {rule_count} 条规则,正在执行...'

                self._update_progress(step_id, 'running',
                                    f'正在执行: {group_name}', detail_msg)

                # 先更新状态为正在执行,显示初始思考过程
                initial_thinking = f"## 🔍 {group_name} (阶段 {i}/4)\n\n### 📋 开始分析\n\n正在启动{group_name}的详细分析,请稍候...\n\n🔄 **AI分析引擎正在处理中...**"

                self._update_progress(step_id, 'running',
                                    f'AI正在分析: {group_name}', detail_msg,
                                    ai_thinking=initial_thinking)

                # 添加延迟确保前端能看到初始状态
                time.sleep(2)

                # 检查是否是第二部分（字段内容与一致性检查），需要分组处理
                if "字段内容与一致性检查" in group_name:
                    step_results, ai_thinking = self._execute_complex_audit_step(
                        group_name, rules_text, data_text, audit_context_summary, step_id
                    )
                else:
                    # 执行单个阶段的审核
                    step_results, ai_thinking = self._execute_audit_step(
                        group_name, rules_text, data_text, audit_context_summary
                    )

                # 格式化并更新AI思考过程（无论JSON解析是否成功都要保存）
                formatted_thinking = None
                if ai_thinking:
                    formatted_thinking = self._format_thinking_process(ai_thinking, group_name)
                    try:
                        print(f"[AI思考] {group_name}: {formatted_thinking[:100]}...")
                    except UnicodeEncodeError:
                        # 处理编码问题,使用安全的输出方式
                        safe_thinking = formatted_thinking[:100].encode('utf-8', errors='ignore').decode('utf-8')
                        print(f"[AI思考] {group_name}: {safe_thinking}...")

                    # 保存当前阶段的思维链
                    thinking_entry = {
                        'phase': group_name,
                        'thinking': formatted_thinking,
                        'timestamp': time.strftime("%Y-%m-%dT%H:%M:%SZ")
                    }
                    self.all_thinking_chains.append(thinking_entry)

                    # 立即更新完整的AI思考过程
                    self._update_progress(step_id, 'running',
                                        f'AI正在分析: {group_name}', detail_msg,
                                        ai_thinking=formatted_thinking)

                    # 添加延迟确保前端能看到完整的思考过程
                    time.sleep(3)
                else:
                    print(f"[警告] {group_name}: 没有获取到AI思维链")
                    # 即使没有AI思维链，也要更新状态
                    self._update_progress(step_id, 'running',
                                        f'AI正在分析: {group_name}', detail_msg)
                
                if step_results:
                    all_results.extend(step_results)

                    # 更新上下文摘要,为下一步做准备
                    new_context = self.prompt_manager.extract_context_for_next_step(step_results)
                    if new_context:
                        audit_context_summary += f"\n{new_context}"

                    # 统计当前步骤结果
                    step_stats = self._calculate_step_stats(step_results)
                    print(f"     [完成] 完成 {step_stats['total']} 条规则 "
                          f"(通过:{step_stats['pass']}, 警告:{step_stats['warning']}, "
                          f"失败:{step_stats['fail']})")

                    # 更新完成状态,使用完整的思维链
                    completed_detail = f"完成 {step_stats['total']} 条规则 (通过:{step_stats['pass']}, 警告:{step_stats['warning']}, 失败:{step_stats['fail']})"

                    # 获取当前完整的思维链用于状态更新
                    current_thinking = self._get_incremental_thinking()

                    self._update_progress(step_id, 'completed', f'{group_name} 完成', completed_detail,
                                        ai_thinking=current_thinking)

                    # 添加短暂延迟,确保状态更新被前端捕获
                    time.sleep(0.5)

                    print(f"     [状态] 阶段 {group_name} 已完成,准备进入下一阶段")
                else:
                    print(f"     [警告] 阶段 {group_name} 执行失败")
                    # 即使执行失败，也要传递AI思维链
                    current_thinking = self._get_incremental_thinking()
                    if formatted_thinking:  # 如果当前阶段有思维链，优先使用
                        current_thinking = formatted_thinking
                    self._update_progress(step_id, 'failed', f'{group_name} 执行失败',
                                        ai_thinking=current_thinking)
            
            # 7. 生成最终报告
            self._update_progress('report-generation', 'running', '正在生成最终报告...')
            print(" [7/7] 审核完成,正在生成最终报告...")
            final_report = self._format_final_report(all_results)

            elapsed_time = time.time() - start_time
            print(f"[完成] 审核完成!耗时 {elapsed_time:.2f} 秒")
            print(f"[统计] 审核摘要: 总计 {final_report['summary']['total_rules_checked']} 条规则")
            print(f"   [成功] 通过: {final_report['summary']['passed_count']}")
            print(f"   [警告] 警告: {final_report['summary']['warning_count']}")
            print(f"   [错误] 失败: {final_report['summary']['failed_count']}")

            # 输出LLM调用统计
            try:
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(__file__)))
                from llm_caller import llm_stats
                llm_stats.print_summary()
            except ImportError as e:
                print(f"⚠️ [统计] 无法导入LLM统计模块: {e}")

            # 注意:报告只生成JSON格式,需要通过HTTP服务器访问
            print("[提示] 提示:请通过HTTP服务器访问报告页面以避免CORS问题")

            # 准备最终统计信息
            final_stats = {
                'total': final_report['summary']['total_rules_checked'],
                'passed': final_report['summary']['passed_count'],
                'warning': final_report['summary']['warning_count'],
                'failed': final_report['summary']['failed_count']
            }

            # 标记审核完成 - 保留完整的思维链历史
            completion_message = f"✅ 审核完成!共检查{final_stats['total']}条规则,通过{final_stats['passed']}条"

            # 获取完整的思维链历史,而不是覆盖
            final_thinking = self._combine_thinking_chains()
            if not final_thinking:
                final_thinking = f"审核流程已全部完成,共处理{len(all_results)}个阶段的规则检查,生成详细审核报告"

            self._update_progress('report-generation', 'audit-complete', completion_message,
                                f'耗时 {elapsed_time:.2f} 秒', final_stats=final_stats,
                                ai_thinking=final_thinking)

            return final_report
            
        except Exception as e:
            error_message = f"系统错误: {str(e)}"
            print(f"[错误] 审核过程中发生错误: {e}")
            self._update_progress('data-loading', 'failed', error_message)
            return self._create_error_report(error_message)
    
    def _execute_audit_step(self, group_name: str, rules_text: str,
                           data_text: str, context_summary: str):
        """
        执行单个审核步骤

        Args:
            group_name: 阶段名称
            rules_text: 规则文本
            data_text: 数据文本
            context_summary: 上下文摘要

        Returns:
            tuple: (该步骤的审核结果, AI思考过程)
        """
        try:
            # 生成提示词
            prompt = self.prompt_manager.create_step_prompt(
                group_name, rules_text, data_text, context_summary
            )

            # 调用LLM获取思维链和结果
            ai_thinking = ""
            response_str = ""

            try:
                ai_thinking, response_str = self.llm_caller.query_with_reasoning(prompt)
                print(f"\n[LLM调试] {group_name} - 获取到思维链长度: {len(ai_thinking)} 字符")
                print(f"[LLM调试] {group_name} - 获取到响应长度: {len(response_str)} 字符")

                # 详细输出思维链内容用于调试
                print(f"\n[LLM调试] {group_name} - 完整思维链内容:")
                print("=" * 100)
                print(ai_thinking)
                print("=" * 100)

                print(f"\n[LLM调试] {group_name} - 完整响应内容:")
                print("=" * 100)
                print(response_str)
                print("=" * 100)

                # 验证思维链内容
                if not ai_thinking or ai_thinking.startswith("错误:"):
                    print(f"[警告] {group_name} - 思维链内容无效,尝试从响应中提取")
                    ai_thinking = self._extract_thinking_process(response_str, group_name, rules_text)

            except Exception as e:
                print(f"[警告] 思维链获取失败,使用传统方法: {e}")
                try:
                    response_str = self.llm_caller.query_semantic_rule(prompt)
                    ai_thinking = self._extract_thinking_process(response_str, group_name, rules_text)
                except Exception as e2:
                    print(f"[错误] 传统方法也失败: {e2}")
                    response_str = ""
                    ai_thinking = self._generate_fallback_thinking(group_name, rules_text)

            # 确保始终有思维链内容
            if not ai_thinking:
                ai_thinking = self._generate_fallback_thinking(group_name, rules_text)
                print(f"[备用] 使用备用思维链: {ai_thinking[:100]}...")

            # 调试信息:检查原始响应
            if not response_str:
                print(f"     [警告] LLM返回空响应")
                return [self._create_error_result(f"阶段 {group_name} - LLM返回空响应", "LLM API调用返回了空字符串")], ai_thinking

            # 检查响应长度，判断是否可能被截断
            response_length = len(response_str)
            print(f"     [监控] 响应长度: {response_length} 字符")

            if response_length > 15000:  # 超过15K字符可能接近token限制
                print(f"     [警告] 响应长度较大({response_length}字符)，可能接近token限制")

            # 检查响应是否以不完整的JSON结尾
            if response_str.rstrip().endswith((',', '{', '[')):
                print(f"     [警告] 响应可能被截断，以不完整的JSON结构结尾")
                if ai_thinking and self.report_manager:
                    try:
                        phase_key = self._get_phase_key_from_group_name(group_name)
                        self.report_manager.update_ai_thinking(
                            phase_key=phase_key,
                            ai_thinking=ai_thinking,
                            phase_name=group_name,
                            status="failed",
                            message="响应被截断，可能超出token限制",
                            detail=f"响应长度: {response_length}, 以'{response_str[-10:]}'结尾"
                        )
                    except Exception as save_error:
                        print(f"     [错误] 保存截断警告失败: {save_error}")

                return [self._create_error_result(f"阶段 {group_name} - 响应被截断", f"响应长度{response_length}字符，可能超出token限制")], ai_thinking

            if response_str.startswith("错误:"):
                print(f"     [警告] LLM返回错误: {response_str}")
                return [self._create_error_result(f"阶段 {group_name} - LLM调用错误", response_str)], ai_thinking

            # 🔥 改进的JSON解析逻辑
            # 首先尝试从完整响应中提取JSON（更可靠的方法）
            print(f"     [调试] 开始JSON提取，原始响应长度: {len(response_str)}")
            print(f"     [调试] 原始响应前200字符: {response_str[:200]}")

            # 优先使用改进的提取方法
            cleaned_response = self._extract_json_from_response(response_str)

            # 如果提取失败，尝试传统清理方法
            if not cleaned_response.strip():
                print(f"     [调试] 改进提取方法失败，尝试传统清理方法")
                cleaned_response = self._clean_llm_response(response_str)

            # 最终检查
            if not cleaned_response.strip():
                print(f"     [错误] 所有JSON提取方法都失败")
                print(f"     [调试] 响应是否包含'第二部分'标记: {'第二部分' in response_str}")
                print(f"     [调试] 响应是否包含'['字符: {'[' in response_str}")
                print(f"     [调试] 响应是否包含']'字符: {']' in response_str}")

                # 即使JSON解析失败，也要保存AI思维链
                if ai_thinking and self.report_manager:
                    try:
                        phase_key = self._get_phase_key_from_group_name(group_name)
                        self.report_manager.update_ai_thinking(
                            phase_key=phase_key,
                            ai_thinking=ai_thinking,
                            phase_name=group_name,
                            status="failed",
                            message="JSON解析失败但思维链已保存",
                            detail=f"响应长度: {len(response_str)}, 包含'[': {'[' in response_str}, 包含']': {']' in response_str}"
                        )
                        print(f"     [新架构] AI思维链已保存到报告文件: {phase_key}")
                    except Exception as save_error:
                        print(f"     [错误] 保存AI思维链失败: {save_error}")

                return [self._create_error_result(f"阶段 {group_name} - 无法提取JSON", "所有JSON提取方法都失败")], ai_thinking

            print(f"     [调试] JSON提取成功，长度: {len(cleaned_response)}")
            print(f"     [调试] 提取的JSON前100字符: {cleaned_response[:100]}")

            step_results = json.loads(cleaned_response)

            # 验证结果格式
            if not isinstance(step_results, list):
                raise ValueError("LLM返回的不是数组格式")

            # 验证每个结果的格式
            for result in step_results:
                if not all(key in result for key in ['rule_id', 'status', 'reason']):
                    raise ValueError("结果格式不完整")

            # 🔥 JSON解析成功：同时写入AI思维链和审核结果
            if self.report_manager:
                phase_key = self._get_phase_key_from_group_name(group_name)

                # 1. 写入AI思维链（优化1）
                if ai_thinking:
                    try:
                        self.report_manager.update_ai_thinking(
                            phase_key=phase_key,
                            ai_thinking=ai_thinking,
                            phase_name=group_name,
                            status="completed",
                            message="JSON解析成功，思维链和结果都已保存",
                            detail=f"成功解析{len(step_results)}条规则结果"
                        )
                        print(f"     [新架构] AI思维链已保存到报告文件: {phase_key}")
                    except Exception as thinking_error:
                        print(f"     [错误] 保存AI思维链失败: {thinking_error}")

                # 2. 写入审核结果（优化2）
                if step_results:
                    try:
                        self.report_manager.update_audit_results(
                            phase_key=phase_key,
                            phase_results=step_results,
                            phase_name=group_name
                        )
                        print(f"     [新架构] 阶段 {phase_key} 的审核结果已实时写入: {len(step_results)} 条规则")
                    except Exception as results_error:
                        print(f"     [错误] 实时写入审核结果失败: {results_error}")

            return step_results, ai_thinking

        except json.JSONDecodeError as e:
            print(f"     [错误] JSON解析失败: {e}")
            print(f"     清理后的响应: {cleaned_response[:200] if 'cleaned_response' in locals() else '未定义'}")

            # 即使JSON解析失败，也要保存AI思维链到报告文件
            if ai_thinking and self.report_manager:
                try:
                    phase_key = self._get_phase_key_from_group_name(group_name)
                    self.report_manager.update_ai_thinking(
                        phase_key=phase_key,
                        ai_thinking=ai_thinking,
                        phase_name=group_name,
                        status="failed",
                        message="JSON解析失败但思维链已保存",
                        detail=f"解析错误: {str(e)}, 响应长度: {len(response_str)}, Token可能超限"
                    )
                    print(f"     [新架构] AI思维链已保存到报告文件: {phase_key}")
                except Exception as save_error:
                    print(f"     [错误] 保存AI思维链失败: {save_error}")

            return [self._create_error_result(f"阶段 {group_name} - JSON解析失败", str(e))], ai_thinking
        except Exception as e:
            print(f"     [错误] 执行失败: {e}")

            # 即使执行失败，也要保存AI思维链到报告文件
            if ai_thinking and self.report_manager:
                try:
                    phase_key = self._get_phase_key_from_group_name(group_name)
                    self.report_manager.update_ai_thinking(
                        phase_key=phase_key,
                        ai_thinking=ai_thinking,
                        phase_name=group_name,
                        status="failed",
                        message="执行失败但思维链已保存",
                        detail=f"执行错误: {str(e)}"
                    )
                    print(f"     [新架构] AI思维链已保存到报告文件: {phase_key}")
                except Exception as save_error:
                    print(f"     [错误] 保存AI思维链失败: {save_error}")

            return [self._create_error_result(f"阶段 {group_name} - 执行失败", str(e))], ai_thinking

    def _execute_complex_audit_step(self, group_name: str, rules_text: str,
                                   data_text: str, context_summary: str, step_id: str):
        """
        执行复杂审核步骤（如第二部分），使用分组处理避免token限制

        Args:
            group_name: 阶段名称
            rules_text: 规则文本
            data_text: 数据文本
            context_summary: 上下文摘要
            step_id: 步骤ID

        Returns:
            tuple: (该步骤的审核结果, AI思考过程)
        """
        try:
            print(f"     [复杂处理] 开始分组处理复杂阶段: {group_name}")

            # 将规则分成两组
            rules_lines = [line.strip() for line in rules_text.split('\n') if line.strip()]

            # 找到规则分割点（大约在中间位置）
            rule_indices = []
            for i, line in enumerate(rules_lines):
                if line.startswith('**规则') and '：' in line:
                    rule_indices.append(i)

            if len(rule_indices) < 2:
                # 如果规则太少，直接使用原方法
                return self._execute_audit_step(group_name, rules_text, data_text, context_summary)

            # 分成两组
            mid_point = len(rule_indices) // 2
            split_index = rule_indices[mid_point]

            group1_lines = rules_lines[:split_index]
            group2_lines = rules_lines[split_index:]

            group1_text = '\n'.join(group1_lines)
            group2_text = '\n'.join(group2_lines)

            print(f"     [分组] 第一组: {len(rule_indices[:mid_point])} 条规则")
            print(f"     [分组] 第二组: {len(rule_indices[mid_point:])} 条规则")

            # 执行第一组
            print(f"     [执行] 处理第一组规则...")
            results1, thinking1 = self._execute_audit_step(
                f"{group_name} (第1组)", group1_text, data_text, context_summary
            )

            # 执行第二组
            print(f"     [执行] 处理第二组规则...")
            results2, thinking2 = self._execute_audit_step(
                f"{group_name} (第2组)", group2_text, data_text, context_summary
            )

            # 合并结果
            all_results = results1 + results2
            combined_thinking = f"{thinking1}\n\n---\n\n{thinking2}"

            print(f"     [合并] 总共处理了 {len(all_results)} 条规则")

            return all_results, combined_thinking

        except Exception as e:
            print(f"     [错误] 复杂步骤执行失败: {e}")
            # 回退到原方法
            return self._execute_audit_step(group_name, rules_text, data_text, context_summary)

    def _extract_thinking_process(self, response_str: str, group_name: str, rules_text: str) -> str:
        """
        从LLM响应中提取思考过程（备用方法,当直接获取思维链失败时使用）

        Args:
            response_str: LLM的完整响应
            group_name: 当前审核阶段名称
            rules_text: 规则文本

        Returns:
            str: 提取的思考过程
        """
        print(f"\n[提取调试] {group_name} - 开始从响应中提取思维链")
        print(f"[提取调试] {group_name} - 响应长度: {len(response_str)} 字符")

        try:
            # 优先查找标准的思考过程格式
            thinking_patterns = [
                "**第一部分:详细思考过程**",
                "思考过程:",
                "分析过程:",
                "推理过程:",
                "### 第一部分:详细思考过程"
            ]

            for pattern in thinking_patterns:
                if pattern in response_str:
                    print(f"[提取调试] {group_name} - 找到思考过程标记: {pattern}")

                    # 分割响应,提取思考过程部分（支持两种冒号格式）
                    for separator in ["**第二部分:审核结果**", "**第二部分：审核结果**"]:
                        parts = response_str.split(separator)
                        if len(parts) >= 2:
                            thinking_part = parts[0]
                            print(f"[提取调试] {group_name} - 思考部分长度: {len(thinking_part)} 字符")

                            # 提取思考过程内容
                            if pattern in thinking_part:
                                thinking_content = thinking_part.split(pattern)[1].strip()
                                print(f"[提取调试] {group_name} - 提取的思考内容长度: {len(thinking_content)} 字符")

                                # 如果内容足够长,直接返回（保留完整内容）
                                if len(thinking_content) > 200:
                                    print(f"[提取调试] {group_name} - 返回完整思考内容")
                                    return thinking_content
                            break

                    # 如果没有找到第二部分分隔符,尝试其他方式
                    thinking_start = response_str.find(pattern)
                    if thinking_start != -1:
                        thinking_content = response_str[thinking_start + len(pattern):].strip()

                        # 查找可能的结束标记
                        end_patterns = ["**第二部分", "### 第二部分", "## 审核结果", "```json"]
                        for end_pattern in end_patterns:
                            if end_pattern in thinking_content:
                                thinking_content = thinking_content.split(end_pattern)[0].strip()
                                break

                        if len(thinking_content) > 200:
                            print(f"[提取调试] {group_name} - 返回提取的思考内容,长度: {len(thinking_content)}")
                            return thinking_content

            # 如果没有找到标准格式的思考过程,尝试从响应中提取关键分析内容
            if any(keyword in response_str for keyword in ['分析', '检查', '判断', '考虑']):
                lines = response_str.split('\n')
                analysis_lines = []

                for line in lines:
                    line = line.strip()
                    if any(keyword in line for keyword in ['分析', '检查', '判断', '考虑', '发现', '确认']):
                        if len(line) > 15 and not line.startswith('{') and not line.startswith('['):
                            analysis_lines.append(line)

                if analysis_lines:
                    return '\n'.join(f"• {line}" for line in analysis_lines[:3])

            # 默认思考过程
            rule_count = self._count_rules_in_text(rules_text)
            return f"• 正在分析{group_name}阶段的{rule_count}条规则\n• 逐一检查每条规则的适用性和合规性\n• 基于提供的单据数据进行判断"

        except Exception as e:
            print(f"     [警告] 提取思考过程失败: {e}")
            rule_count = self._count_rules_in_text(rules_text)
            return f"• 正在分析{group_name}阶段的{rule_count}条规则\n• 执行标准审核流程\n• 基于单据数据进行合规性检查"

    def _format_thinking_process(self, raw_thinking: str, group_name: str) -> str:
        """
        格式化思维链文本,保留完整的AI推理过程

        Args:
            raw_thinking: 原始思维链文本
            group_name: 当前审核阶段名称

        Returns:
            str: 格式化后的思考过程
        """
        if not raw_thinking or raw_thinking.startswith("错误:"):
            return f"• 正在执行{group_name}的规则检查\n• 分析单据数据的完整性和合规性\n• 应用相关的审核标准和规则"

        # 添加详细的日志输出,便于调试
        print(f"\n[思维链调试] {group_name} - 原始思维链长度: {len(raw_thinking)} 字符")
        print(f"[思维链调试] {group_name} - 原始内容前500字符:")
        print("-" * 80)
        print(raw_thinking[:500])
        print("-" * 80)

        # 保留完整的思维链内容,只做基本的格式化
        formatted_thinking = raw_thinking.strip()

        # 如果内容太短,可能不是真正的思维链
        if len(formatted_thinking) < 100:
            print(f"[警告] {group_name} - 思维链内容过短,可能不是真正的AI推理过程")
            return f"• 正在执行{group_name}的智能审核分析\n• 原始内容: {formatted_thinking}"

        # 添加阶段标识,但保留完整内容
        final_thinking = f"## 🔍 {group_name} (阶段详细分析)\n\n{formatted_thinking}"

        print(f"[思维链调试] {group_name} - 格式化后长度: {len(final_thinking)} 字符")
        print(f"[思维链调试] {group_name} - 格式化后前200字符: {final_thinking[:200]}...")

        return final_thinking

    def _generate_fallback_thinking(self, group_name: str, rules_text: str) -> str:
        """
        生成备用思维链（当LLM调用失败时使用）

        Args:
            group_name: 当前审核阶段名称
            rules_text: 规则文本

        Returns:
            str: 备用思维链文本
        """
        rule_count = self._count_rules_in_text(rules_text)

        phase_thinking = {
            '附件完整性检查': [
                f"正在检查{rule_count}条附件相关规则",
                "验证必需文件是否已上传",
                "检查文件格式和完整性",
                "确认附件与申请内容的匹配性"
            ],
            '字段一致性检查': [
                f"正在执行{rule_count}条一致性验证规则",
                "比对不同文档中的关键字段",
                "验证日期, 金额, 人员等信息的一致性",
                "识别可能的数据不匹配问题"
            ],
            '金额标准检查': [
                f"正在应用{rule_count}条金额标准规则",
                "检查费用是否在预算范围内",
                "验证金额的合理性和必要性",
                "分析消费标准的合规性"
            ],
            '合规性检查': [
                f"正在执行{rule_count}条合规性规则",
                "检查是否符合公司政策",
                "识别潜在的合规风险",
                "应用行业标准和法规要求"
            ]
        }

        steps = phase_thinking.get(group_name, [
            f"正在分析{group_name}阶段的{rule_count}条规则",
            "逐一检查每条规则的适用性",
            "基于提供的数据进行智能判断",
            "生成详细的审核建议"
        ])

        return '\n'.join(f"• {step}" for step in steps)

    def _combine_thinking_chains(self) -> str:
        """
        合并所有阶段的思维链,用于前端显示

        Returns:
            str: 合并后的思维链文本
        """
        if not self.all_thinking_chains:
            return ""

        combined_parts = []

        for i, entry in enumerate(self.all_thinking_chains, 1):
            phase_name = entry['phase']
            thinking_content = entry['thinking']

            # 添加阶段标题
            combined_parts.append(f"## 🔍 {phase_name} (阶段 {i}/4)")
            combined_parts.append("")  # 空行

            # 添加思维链内容
            if thinking_content:
                combined_parts.append(thinking_content)
            else:
                combined_parts.append(f"• 正在执行{phase_name}的规则检查")
                combined_parts.append(f"• 分析相关数据的完整性和合规性")

            # 添加分隔线（除了最后一个）
            if i < len(self.all_thinking_chains):
                combined_parts.append("")
                combined_parts.append("---")
                combined_parts.append("")

        # 添加总结
        if len(self.all_thinking_chains) >= 4:
            combined_parts.append("")
            combined_parts.append("## 🎯 审核总结")
            combined_parts.append("")
            combined_parts.append("• ✅ 第一部分:附件完整性检查 - 已完成")
            combined_parts.append("• ✅ 第二部分:字段内容与一致性检查 - 已完成")
            combined_parts.append("• ✅ 第三部分:金额与标准检查 - 已完成")
            combined_parts.append("• ✅ 第四部分:八项规定精神 - 已完成")
            combined_parts.append("• 🎉 所有审核阶段已完成,生成详细审核报告")

        return '\n'.join(combined_parts)

    def _get_incremental_thinking(self) -> str:
        """
        获取增量思维链内容,用于前端增量显示

        Returns:
            str: 当前完整的思维链内容（支持增量更新）
        """
        if not self.all_thinking_chains:
            return ""

        # 返回完整的思维链内容,前端会自动处理增量更新
        combined_parts = []

        for i, entry in enumerate(self.all_thinking_chains, 1):
            phase_name = entry['phase']
            thinking_content = entry['thinking']

            # 添加阶段标题
            combined_parts.append(f"## 🔍 {phase_name} (阶段 {i}/4)")
            combined_parts.append("")  # 空行

            # 添加思维链内容
            if thinking_content:
                combined_parts.append(thinking_content)
            else:
                combined_parts.append(f"• 正在执行{phase_name}的规则检查")
                combined_parts.append(f"• 分析相关数据的完整性和合规性")

            # 如果是最后一个阶段且正在进行中,添加进行中标识
            if i == len(self.all_thinking_chains):
                combined_parts.append("")
                combined_parts.append("🔄 **当前阶段正在进行中...**")

            # 添加分隔线（除了最后一个）
            if i < len(self.all_thinking_chains):
                combined_parts.append("")
                combined_parts.append("---")
                combined_parts.append("")

        return '\n'.join(combined_parts)

    def _clean_llm_response_with_thinking(self, response: str) -> str:
        """
        清理包含思考过程的LLM响应,提取JSON部分

        Args:
            response: 原始响应

        Returns:
            str: 清理后的JSON字符串
        """
        if not response:
            return ""

        # 如果响应包含思考过程,提取JSON部分
        if "**第二部分:审核结果**" in response:
            parts = response.split("**第二部分:审核结果**")
            if len(parts) >= 2:
                json_part = parts[1].strip()
            else:
                json_part = response
        else:
            json_part = response

        # 使用原有的清理逻辑
        return self._clean_llm_response(json_part)
    
    def _clean_llm_response(self, response: str) -> str:
        """
        清理LLM响应,移除markdown格式等

        Args:
            response: 原始响应

        Returns:
            str: 清理后的JSON字符串
        """
        if not response:
            return ""

        original_response = response

        # 移除可能的markdown代码块标记
        if "```json" in response:
            start = response.find("```json") + 7
            end = response.rfind("```")
            if end > start:
                response = response[start:end]
        elif "```" in response:
            start = response.find("```") + 3
            end = response.rfind("```")
            if end > start:
                response = response[start:end]

        # 清理空白字符
        cleaned = response.strip()

        # 如果清理后为空,尝试查找JSON数组
        if not cleaned and original_response:
            # 尝试查找JSON数组的开始和结束
            start_bracket = original_response.find('[')
            end_bracket = original_response.rfind(']')
            if start_bracket != -1 and end_bracket != -1 and end_bracket > start_bracket:
                cleaned = original_response[start_bracket:end_bracket + 1]

        return cleaned

    def _extract_json_from_response(self, response: str) -> str:
        """
        从完整响应中提取JSON数组部分（改进版）

        Args:
            response: 完整的LLM响应

        Returns:
            str: 提取的JSON字符串
        """
        if not response:
            return ""

        # 方法1: 查找"第二部分：审核结果"后的JSON
        if "第二部分：审核结果" in response:
            start_pos = response.find("第二部分：审核结果")
            response_part = response[start_pos:]

            # 在这部分中查找JSON数组
            start_bracket = response_part.find('[')
            if start_bracket != -1:
                # 找到匹配的结束括号
                bracket_count = 0
                end_pos = start_bracket
                for i, char in enumerate(response_part[start_bracket:], start_bracket):
                    if char == '[':
                        bracket_count += 1
                    elif char == ']':
                        bracket_count -= 1
                        if bracket_count == 0:
                            end_pos = i
                            break

                if bracket_count == 0:
                    json_str = response_part[start_bracket:end_pos + 1]
                    # 验证JSON格式
                    try:
                        json.loads(json_str)
                        return json_str
                    except json.JSONDecodeError:
                        pass

        # 方法2: 查找最后一个完整的JSON数组
        start_bracket = response.rfind('[')
        if start_bracket != -1:
            # 从最后一个[开始，向后查找匹配的]
            bracket_count = 0
            end_pos = len(response) - 1

            for i in range(start_bracket, len(response)):
                char = response[i]
                if char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
                    if bracket_count == 0:
                        end_pos = i
                        break

            if bracket_count == 0:
                json_str = response[start_bracket:end_pos + 1]
                # 验证JSON格式
                try:
                    json.loads(json_str)
                    return json_str
                except json.JSONDecodeError:
                    pass

        # 方法3: 使用正则表达式查找JSON数组
        import re
        json_pattern = r'\[\s*\{[^[\]]*\}(?:\s*,\s*\{[^[\]]*\})*\s*\]'
        matches = re.findall(json_pattern, response, re.DOTALL)

        # 从后往前尝试每个匹配，直到找到有效的JSON
        for match in reversed(matches):
            try:
                json.loads(match)
                return match
            except json.JSONDecodeError:
                continue

        # 方法4: 简单的括号匹配（作为最后的备用方案）
        all_brackets = []
        for i, char in enumerate(response):
            if char in ['[', ']']:
                all_brackets.append((char, i))

        # 查找最大的有效JSON数组
        for i in range(len(all_brackets)):
            if all_brackets[i][0] == '[':
                bracket_count = 0
                start_pos = all_brackets[i][1]

                for j in range(i, len(all_brackets)):
                    if all_brackets[j][0] == '[':
                        bracket_count += 1
                    elif all_brackets[j][0] == ']':
                        bracket_count -= 1
                        if bracket_count == 0:
                            end_pos = all_brackets[j][1]
                            json_str = response[start_pos:end_pos + 1]
                            try:
                                json.loads(json_str)
                                return json_str
                            except json.JSONDecodeError:
                                break

        return ""

    def _get_phase_key_from_group_name(self, group_name: str) -> str:
        """
        从组名获取阶段键值

        Args:
            group_name: 组名（如"第一部分：附件完整性检查"）

        Returns:
            str: 阶段键值（如"phase1"）
        """
        phase_mapping = {
            # 标准格式
            '第一部分：附件完整性检查': 'phase1',
            '第二部分：字段内容与一致性检查': 'phase2',
            '第三部分：金额与标准检查': 'phase3',
            '第四部分：八项规定精神': 'phase4',
            # 简化格式
            '附件完整性检查': 'phase1',
            '字段内容与一致性检查': 'phase2',
            '金额与标准检查': 'phase3',
            '八项规定精神': 'phase4',
            # 英文格式
            'attachment-check': 'phase1',
            'field-consistency': 'phase2',
            'amount-standard': 'phase3',
            'compliance-check': 'phase4',
            'finished': 'finished'
        }

        return phase_mapping.get(group_name, group_name.lower().replace(' ', '_'))

    def _calculate_step_stats(self, step_results: List[Dict[str, Any]]) -> Dict[str, int]:
        """计算步骤统计信息"""
        stats = {"total": len(step_results), "pass": 0, "warning": 0, "fail": 0, "unknown": 0}

        print(f"[调试] 开始统计步骤结果，共{len(step_results)}条")
        for i, result in enumerate(step_results):
            status = result.get("status", "").lower().strip()
            print(f"[调试] 第{i+1}条规则: rule_id='{result.get('rule_id', '')}', status='{result.get('status', '')}' -> 处理后='{status}'")

            # 支持多种状态格式
            if status in ["通过", "pass", "passed", "success", "成功"]:
                stats["pass"] += 1
                print(f"[调试] 归类为: 通过")
            elif status in ["警告", "warning", "warn", "注意"]:
                stats["warning"] += 1
                print(f"[调试] 归类为: 警告")
            elif status in ["不通过", "失败", "fail", "failed", "error", "错误", "执行失败"]:
                stats["fail"] += 1
                print(f"[调试] 归类为: 失败")
            elif status in ["无法判断", "unknown", "无法确定", "不确定"]:
                stats["unknown"] += 1
                print(f"[调试] 归类为: 无法判断")
            else:
                print(f"[调试] 未识别的状态: '{result.get('status', '')}' -> 归类为: 未知")
                stats["unknown"] += 1

        print(f"[调试] 统计结果: 总计={stats['total']}, 通过={stats['pass']}, 警告={stats['warning']}, 失败={stats['fail']}, 未知={stats['unknown']}")

        return stats

    def _build_combined_thinking(self, phases_history: Dict, current_thinking: str, current_phase: str) -> str:
        """构建包含所有阶段的组合思维链"""

        # 定义阶段顺序和名称
        phase_order = [
            ("phase1", "🔍 附件完整性检查 (阶段 1/4)"),
            ("phase2", "🔍 字段内容与一致性检查 (阶段 2/4)"),
            ("phase3", "🔍 金额与标准检查 (阶段 3/4)"),
            ("phase4", "🔍 八项规定精神 (阶段 4/4)")
        ]

        combined_parts = []

        # 添加已完成阶段的思维链
        for phase_key, phase_title in phase_order:
            if phase_key == "phase2":
                # 特殊处理第二部分：合并分组数据
                phase2_content = self._build_phase2_combined_content(phases_history)
                if phase2_content:
                    combined_parts.append(f"## {phase_title}")
                    combined_parts.append("")
                    combined_parts.append(phase2_content)
                    combined_parts.append("")
                    combined_parts.append("---")
                    combined_parts.append("")
                elif phase_key == current_phase and current_thinking:
                    # 当前正在进行的第二阶段
                    combined_parts.append(f"## {phase_title}")
                    combined_parts.append("")
                    combined_parts.append(current_thinking)
                    combined_parts.append("")
            elif phase_key in phases_history:
                phase_data = phases_history[phase_key]
                combined_parts.append(f"## {phase_title}")
                combined_parts.append("")
                combined_parts.append(phase_data.get("ai_thinking", ""))
                combined_parts.append("")
                combined_parts.append("---")
                combined_parts.append("")
            elif phase_key == current_phase and current_thinking:
                # 当前正在进行的阶段
                combined_parts.append(f"## {phase_title}")
                combined_parts.append("")
                combined_parts.append(current_thinking)
                combined_parts.append("")

        # 如果没有任何内容，返回默认消息
        if not combined_parts:
            return "系统就绪，等待开始AI审核分析..."

        # 添加总结
        completed_phases = self._count_completed_phases(phases_history, phase_order)
        if completed_phases > 0:
            combined_parts.append("## 🎯 审核总结")
            combined_parts.append("")
            combined_parts.append(f"• ✅ 已完成 {completed_phases}/4 个审核阶段")
            if current_phase != "finished":
                current_phase_name = next((title for key, title in phase_order if key == current_phase), current_phase)
                combined_parts.append(f"• 🔄 当前阶段: {current_phase_name}")
            combined_parts.append("")

        return "\n".join(combined_parts)

    def _build_phase2_combined_content(self, phases_history: Dict) -> str:
        """构建第二部分的合并内容"""
        phase2_parts = []

        # 查找第二部分的分组数据
        group1_key = "第二部分：字段内容与一致性检查_(第1组)"
        group2_key = "第二部分：字段内容与一致性检查_(第2组)"

        # 添加第1组内容
        if group1_key in phases_history:
            group1_data = phases_history[group1_key]
            phase2_parts.append("### 第1组 (规则6-14)")
            phase2_parts.append("")
            phase2_parts.append(group1_data.get("ai_thinking", ""))
            phase2_parts.append("")

        # 添加第2组内容
        if group2_key in phases_history:
            group2_data = phases_history[group2_key]
            if phase2_parts:  # 如果已有第1组内容，添加分隔符
                phase2_parts.append("---")
                phase2_parts.append("")
            phase2_parts.append("### 第2组 (规则15-24)")
            phase2_parts.append("")
            phase2_parts.append(group2_data.get("ai_thinking", ""))
            phase2_parts.append("")

        # 如果没有找到分组数据，尝试查找标准的phase2
        if not phase2_parts and "phase2" in phases_history:
            phase2_data = phases_history["phase2"]
            phase2_parts.append(phase2_data.get("ai_thinking", ""))

        return "\n".join(phase2_parts)

    def _count_completed_phases(self, phases_history: Dict, phase_order: List) -> int:
        """计算已完成的阶段数量，正确处理第二部分的分组数据"""
        completed_count = 0

        for phase_key, _ in phase_order:
            if phase_key == "phase2":
                # 检查第二部分的分组数据
                group1_key = "第二部分：字段内容与一致性检查_(第1组)"
                group2_key = "第二部分：字段内容与一致性检查_(第2组)"

                # 如果有任一分组数据或标准phase2数据，则认为第二部分已完成
                if (group1_key in phases_history or
                    group2_key in phases_history or
                    phase_key in phases_history):
                    completed_count += 1
            elif phase_key in phases_history:
                completed_count += 1

        return completed_count

    def _format_final_report(self, all_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        将所有步骤的结果转换为与前端兼容的最终报告格式

        Args:
            all_results: 所有审核结果

        Returns:
            Dict[str, Any]: 最终报告
        """
        # 状态映射:将中文状态转换为系统状态
        status_map = {
            "通过": "PASS",
            "不通过": "FAIL",
            "警告": "WARNING",
            "无法判断": "WARNING",  # 无法判断也归类为警告
            "执行失败": "FAIL"
        }

        # 转换详细结果
        details = []
        actual_rules_count = 0  # 实际审核的规则数量
        actual_passed_count = 0  # 实际通过的规则数量
        actual_failed_count = 0  # 实际失败的规则数量
        actual_warning_count = 0  # 实际警告的规则数量

        for res in all_results:
            rule_id = res.get("rule_id", "未知规则")
            status = status_map.get(res.get("status"), "FAIL")

            details.append({
                "rule_id": rule_id,
                "status": status,
                "message": res.get("reason", "无详细信息")
            })

            # 计算实际规则数量,考虑合并规则
            rule_count = self._count_actual_rules(rule_id)
            actual_rules_count += rule_count

            # 根据状态累加实际数量
            if status == 'PASS':
                actual_passed_count += rule_count
            elif status == 'FAIL':
                actual_failed_count += rule_count
            elif status == 'WARNING':
                actual_warning_count += rule_count

        # 计算摘要统计
        summary = {
            "total_rules_checked": actual_rules_count,  # 使用实际规则数量
            "passed_count": actual_passed_count,        # 使用实际通过数量
            "failed_count": actual_failed_count,        # 使用实际失败数量
            "warning_count": actual_warning_count       # 使用实际警告数量
        }

        # 生成审批意见
        review_comments = self._generate_review_comments(details)

        # 新架构：直接使用报告文件管理器更新最终报告
        if self.report_manager:
            try:
                # 直接更新报告文件中的最终数据
                self.report_manager.update_final_report(summary, details, review_comments)

                # 从报告文件获取完整数据（包含AI思维链）
                final_report = self.report_manager.get_report_data()

                print("[新架构] 最终报告已直接更新到报告文件")
                return final_report

            except Exception as e:
                print(f"[错误] 新架构报告更新失败，回退到传统模式: {e}")

        # 传统模式：从状态文件提取AI思维链数据（保持兼容性）
        ai_thinking_data = self._extract_ai_thinking_for_report()

        return {
            "summary": summary,
            "details": details,
            "review_comments": review_comments,
            "ai_thinking_chain": ai_thinking_data,  # 新增：AI思维链数据
            "audit_metadata": {
                "version": "2.0",
                "audit_type": "stepwise_intelligent_audit",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "document_number": getattr(self, 'current_document_number', 'default'),
                "ai_thinking_included": True  # 标记包含AI思维链
            }
        }

    def _count_actual_rules(self, rule_id: str) -> int:
        """
        计算rule_id对应的实际规则数量,处理合并规则的情况

        Args:
            rule_id: 规则ID字符串

        Returns:
            int: 实际规则数量
        """
        import re

        # 检查是否是合并规则（如"规则21-22", "规则29-30"等）
        merge_pattern = r'规则(\d+)-(\d+)'
        match = re.search(merge_pattern, rule_id)

        if match:
            start_num = int(match.group(1))
            end_num = int(match.group(2))
            return end_num - start_num + 1  # 计算范围内的规则数量
        else:
            return 1  # 单个规则

    def _count_rules_in_text(self, rules_text: str) -> int:
        """
        计算规则文本中包含的规则数量

        Args:
            rules_text: 规则文本

        Returns:
            int: 规则数量
        """
        import re
        # 匹配 "规则X:" 或 "**规则X:" 的模式
        rule_pattern = r'\*?\*?规则\d+[::]'
        matches = re.findall(rule_pattern, rules_text)
        return len(matches)
    
    def _create_error_result(self, rule_id: str, error_msg: str) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            "rule_id": rule_id,
            "status": "执行失败", 
            "reason": error_msg
        }

    def _generate_review_comments(self, details: List[Dict[str, Any]]) -> str:
        """
        生成审批意见,仅关注警告和失败的规则

        Args:
            details: 审核详细结果列表

        Returns:
            str: 审批意见文本
        """
        # 筛选出警告和失败的规则
        issues = []
        for detail in details:
            if detail.get("status") in ["WARNING", "FAIL"]:
                issues.append(detail)

        # 如果没有问题,返回通过意见
        if not issues:
            return "经审核,本次业务招待费报销单据齐全,各项信息填写准确,符合公司相关制度要求,建议予以通过."

        # 生成问题描述
        comments = []
        comments.append("经审核,本次业务招待费报销存在以下需要关注的问题:")
        comments.append("")

        for i, issue in enumerate(issues, 1):
            rule_id = issue.get("rule_id", "")
            message = issue.get("message", "")
            status = issue.get("status", "")

            # 将技术性描述转换为业务语言
            business_message = self._convert_to_business_language(rule_id, message, status)
            comments.append(f"{i}. {business_message}")

        comments.append("")

        # 根据问题严重程度给出建议
        has_fail = any(issue.get("status") == "FAIL" for issue in issues)
        if has_fail:
            comments.append("鉴于存在不符合规定的情况,建议申请人补充相关材料或说明后重新提交审核.")
        else:
            comments.append("以上问题虽不影响报销的合规性,但建议申请人在今后的报销中注意相关要求,确保信息填写的准确性和完整性.基于当前情况,可予以通过.")

        return "\n".join(comments)

    def _extract_ai_thinking_for_report(self) -> Dict[str, Any]:
        """
        从audit_state.json中提取AI思维链数据用于审核报告

        Returns:
            Dict[str, Any]: 包含AI思维链的结构化数据
        """
        try:
            # 读取当前状态文件
            state_file_path = os.path.join(os.path.dirname(__file__), '..', 'audit_state.json')

            if not os.path.exists(state_file_path):
                return self._create_empty_thinking_data()

            with open(state_file_path, 'r', encoding='utf-8') as f:
                state_data = json.load(f)

            # 提取思维链相关数据
            ai_thinking_data = {
                "combined_thinking": state_data.get("ai_thinking", ""),
                "phases_history": {},
                "extraction_metadata": {
                    "extracted_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "audit_id": state_data.get("audit_id", ""),
                    "audit_status": state_data.get("audit_status", ""),
                    "completion_time": state_data.get("completion_time", "")
                }
            }

            # 提取各阶段的思维链历史
            phases_history = state_data.get("phases_history", {})
            for phase_key, phase_data in phases_history.items():
                if isinstance(phase_data, dict) and "ai_thinking" in phase_data:
                    ai_thinking_data["phases_history"][phase_key] = {
                        "phase_name": phase_data.get("phase_name", phase_key),
                        "ai_thinking": phase_data.get("ai_thinking", ""),
                        "status": phase_data.get("status", ""),
                        "timestamp": phase_data.get("timestamp", ""),
                        "message": phase_data.get("message", ""),
                        "detail": phase_data.get("detail", "")
                    }

            return ai_thinking_data

        except Exception as e:
            print(f"[警告] 提取AI思维链数据时出错: {e}")
            return self._create_empty_thinking_data()

    def _create_empty_thinking_data(self) -> Dict[str, Any]:
        """
        创建空的AI思维链数据结构

        Returns:
            Dict[str, Any]: 空的思维链数据
        """
        return {
            "combined_thinking": "AI思维链数据不可用",
            "phases_history": {},
            "extraction_metadata": {
                "extracted_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                "audit_id": "",
                "audit_status": "unknown",
                "completion_time": "",
                "error": "无法提取思维链数据"
            }
        }

    def _convert_to_business_language(self, rule_id: str, message: str, status: str = "警告") -> str:
        """
        将技术性的规则检查结果转换为业务友好的语言

        Args:
            rule_id: 规则ID
            message: 原始消息
            status: 状态

        Returns:
            str: 业务友好的描述
        """
        # 根据规则类型和消息内容生成业务描述
        if "招待事由与项目状态" in rule_id:
            return "招待事由与项目用途的描述存在不一致,建议核实招待的具体目的是否与项目需求相符"
        elif "项目相关性" in rule_id:
            return "招待事由与项目名称的关联性不够明确,建议在审批表中更清楚地说明与项目的关系"
        elif "日期" in rule_id and "一致性" in rule_id:
            return "相关单据的日期信息存在不一致,请核实各项日期填写是否准确"
        elif "金额" in rule_id and ("一致性" in rule_id or "超" in rule_id):
            return "费用金额方面存在异常,请核实实际消费金额与预算, 发票等是否匹配"
        elif "人数" in rule_id:
            return "招待人数信息存在不一致,请核实实际参与人数"
        elif "附件" in rule_id or "上传" in rule_id:
            return "相关附件材料可能缺失或不完整,请补充必要的证明文件"
        elif "审批" in rule_id:
            return "事前审批流程存在问题,请确认审批手续是否完整"
        elif "发票" in rule_id:
            return "发票信息存在异常,请核实发票的真实性和准确性"
        elif "消费" in rule_id and ("超标" in rule_id or "超量" in rule_id):
            return "消费标准可能超出规定范围,请核实是否符合公司招待标准"
        elif "公职人员" in rule_id or "党政军" in rule_id:
            return "招待对象涉及敏感人员,需要特别关注合规性要求"
        else:
            # 通用转换:提取关键信息
            check_item = self._extract_check_item(rule_id)
            if "不满足" in message or "不符合" in message or "不一致" in message:
                return f"在{check_item}方面存在不符合要求的情况,需要进一步核实"
            elif "未发现" in message or "缺失" in message:
                return f"在{check_item}方面可能存在缺失,建议补充相关信息"
            else:
                return f"在{check_item}方面需要关注,请核实相关信息的准确性"

    def _extract_check_item(self, rule_id: str) -> str:
        """
        从规则ID中提取检查项目名称

        Args:
            rule_id: 规则ID

        Returns:
            str: 检查项目名称
        """
        # 移除规则编号前缀
        if ":" in rule_id:
            item = rule_id.split(":", 1)[1]
        else:
            item = rule_id

        # 移除"检查"等前缀词
        item = item.replace("检查", "").replace("是否", "").strip()

        return item if item else "相关信息"

    def _create_error_report(self, error_msg: str) -> Dict[str, Any]:
        """创建错误报告"""
        return {
            "summary": {
                "total_rules_checked": 0,
                "passed_count": 0,
                "failed_count": 1,
                "warning_count": 0
            },
            "details": [{
                "rule_id": "系统错误",
                "status": "FAIL",
                "message": error_msg
            }],
            "audit_metadata": {
                "version": "2.0",
                "audit_type": "stepwise_intelligent_audit", 
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "error": True
            }
        }
